# CUDA Demo Makefile

# 编译器设置
NVCC = nvcc
CC = gcc

# 编译标志
NVCC_FLAGS = -O2 -arch=sm_50 -std=c++11
CUDA_LIBS = -lcudart

# 目标文件
TARGET = cuda_demo
SOURCE = cuda_demo.cu

# 默认目标
all: $(TARGET)

# 编译CUDA程序
$(TARGET): $(SOURCE)
	@echo "正在编译CUDA程序..."
	$(NVCC) $(NVCC_FLAGS) $(SOURCE) -o $(TARGET) $(CUDA_LIBS)
	@echo "编译完成: $(TARGET)"

# 运行程序
run: $(TARGET)
	@echo "运行CUDA Demo..."
	./$(TARGET)

# 清理编译文件
clean:
	@echo "清理编译文件..."
	rm -f $(TARGET) *.o

# 检查CUDA环境
check-cuda:
	@echo "检查CUDA环境..."
	@which nvcc > /dev/null 2>&1 && echo "✓ nvcc 编译器已找到" || echo "✗ nvcc 编译器未找到"
	@nvidia-smi > /dev/null 2>&1 && echo "✓ NVIDIA GPU 驱动正常" || echo "✗ NVIDIA GPU 驱动异常"

# 显示帮助信息
help:
	@echo "可用的make目标:"
	@echo "  all        - 编译程序 (默认)"
	@echo "  run        - 编译并运行程序"
	@echo "  clean      - 清理编译文件"
	@echo "  check-cuda - 检查CUDA环境"
	@echo "  help       - 显示此帮助信息"

# 声明伪目标
.PHONY: all run clean check-cuda help
