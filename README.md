# CUDA Demo - CPU死循环调用GPU并等待执行完毕

这个demo展示了如何在CPU上通过死循环调用GPU执行运算，每次循环都等待GPU执行完毕，并且每次执行后重置内存和显存数据。

## 功能特性

- **死循环执行**: 使用while循环持续调用GPU，直到收到中断信号
- **流同步**: 使用 `cudaStreamSynchronize()` 只同步当前流，不影响其他GPU操作
- **数据重置**: 每次循环后重置主机内存和GPU显存数据
- **异步操作**: 使用CUDA流进行异步内存传输和kernel执行
- **性能计时**: 使用CUDA事件测量每次GPU执行的时间
- **结果验证**: 验证GPU计算结果的正确性
- **交替操作**: 循环中交替执行向量加法和向量乘法
- **信号处理**: 支持Ctrl+C优雅退出
- **错误处理**: 完整的CUDA错误检查机制

## 系统要求

- NVIDIA GPU (支持CUDA)
- CUDA Toolkit (版本 8.0 或更高)
- GCC 编译器
- Linux/Windows 系统

## 编译和运行

### 1. 检查CUDA环境
```bash
make check-cuda
```

### 2. 编译程序
```bash
make
# 或者
make all
```

### 3. 运行程序
```bash
make run
# 或者直接运行
./cuda_demo
```

### 4. 清理编译文件
```bash
make clean
```

## 程序输出示例

```
=== CUDA Demo: CPU死循环调用GPU并等待执行完毕 ===
按 Ctrl+C 退出程序

数组大小: 1048576 元素
线程块大小: 256
网格大小: 4096
开始死循环执行...

--- 第 1 次循环 ---
执行向量加法...
GPU执行时间: 1.234 ms
结果验证: 通过
前5个结果: 3.00 3.10 3.20 3.30 3.40
内存和显存已重置

--- 第 2 次循环 ---
执行向量乘法...
GPU执行时间: 1.156 ms
结果验证: 通过
前5个结果: 4.00 4.22 4.44 4.66 4.88
内存和显存已重置

...

^C
收到信号 2，准备退出...
=== 循环已停止，总共执行了 1523 次 ===
资源清理完成
```

## 代码结构

### 主要文件
- `cuda_demo.cu` - 主程序文件，包含CUDA kernel和主函数
- `Makefile` - 编译配置文件
- `README.md` - 使用说明文档

### 核心功能

#### 1. CUDA Kernel函数
```cuda
__global__ void vectorAdd(float* a, float* b, float* c, int n)
__global__ void vectorMul(float* a, float* b, float* c, int n)
```

#### 2. 流同步机制
```cuda
// 创建CUDA流
cudaStream_t stream;
cudaStreamCreate(&stream);

// 在流中启动kernel
vectorAdd<<<blocksPerGrid, threadsPerBlock, 0, stream>>>(d_a, d_b, d_c, N);

// 只同步当前流，不影响其他GPU操作
cudaStreamSynchronize(stream);
```

#### 3. 性能测量
```cuda
cudaEvent_t start, stop;
cudaEventRecord(start);
// ... GPU操作 ...
cudaEventRecord(stop);
cudaEventSynchronize(stop);
cudaEventElapsedTime(&milliseconds, start, stop);
```

## 关键技术点

1. **流同步**: 使用 `cudaStreamSynchronize()` 只同步当前流，避免影响其他GPU操作
2. **数据重置**: 每次循环后使用 `cudaMemsetAsync()` 和 `memset()` 重置显存和内存
3. **异步操作**: 使用 `cudaMemcpyAsync()` 进行异步内存传输
4. **死循环控制**: 使用信号处理机制优雅退出死循环
5. **错误检查**: 使用宏 `CUDA_CHECK` 进行完整的错误检查
6. **内存管理**: 正确的设备内存分配和释放
7. **性能监控**: 使用CUDA事件精确测量GPU执行时间
8. **结果验证**: 将GPU结果复制回CPU进行验证

## 自定义参数

可以修改以下参数来调整程序行为：

```cpp
const int N = 1024 * 1024;  // 数组大小
int threadsPerBlock = 256;   // 每个线程块的线程数
usleep(50000);              // 循环间隔时间（微秒）
```

## 故障排除

### 编译错误
- 确保安装了CUDA Toolkit
- 检查nvcc编译器路径
- 验证GPU架构设置 (`-arch=sm_50`)

### 运行时错误
- 检查NVIDIA驱动程序
- 确认GPU支持所需的计算能力
- 验证内存分配是否成功

## 扩展建议

- 添加更多类型的GPU操作
- 实现异步执行对比
- 添加多GPU支持
- 集成性能分析工具
