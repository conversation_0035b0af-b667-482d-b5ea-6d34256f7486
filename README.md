# CUDA Demo - CPU循环调用GPU并等待执行完毕

这个demo展示了如何在CPU上通过for循环调用GPU执行运算，并且每次循环都等待GPU执行完毕。

## 功能特性

- **同步执行**: 每次GPU调用后都使用 `cudaDeviceSynchronize()` 等待执行完毕
- **性能计时**: 使用CUDA事件测量每次GPU执行的时间
- **结果验证**: 验证GPU计算结果的正确性
- **交替操作**: 循环中交替执行向量加法和向量乘法
- **错误处理**: 完整的CUDA错误检查机制

## 系统要求

- NVIDIA GPU (支持CUDA)
- CUDA Toolkit (版本 8.0 或更高)
- GCC 编译器
- Linux/Windows 系统

## 编译和运行

### 1. 检查CUDA环境
```bash
make check-cuda
```

### 2. 编译程序
```bash
make
# 或者
make all
```

### 3. 运行程序
```bash
make run
# 或者直接运行
./cuda_demo
```

### 4. 清理编译文件
```bash
make clean
```

## 程序输出示例

```
=== CUDA Demo: CPU循环调用GPU并等待执行完毕 ===

数组大小: 1048576 元素
线程块大小: 256
网格大小: 4096
循环次数: 5

--- 第 1 次循环 ---
执行向量加法...
GPU执行时间: 1.234 ms
结果验证: 通过
前5个结果: 3.00 3.10 3.20 3.30 3.40 

--- 第 2 次循环 ---
执行向量乘法...
GPU执行时间: 1.156 ms
结果验证: 通过
前5个结果: 2.00 2.20 2.42 2.66 2.92 

...

=== 所有循环执行完毕 ===
```

## 代码结构

### 主要文件
- `cuda_demo.cu` - 主程序文件，包含CUDA kernel和主函数
- `Makefile` - 编译配置文件
- `README.md` - 使用说明文档

### 核心功能

#### 1. CUDA Kernel函数
```cuda
__global__ void vectorAdd(float* a, float* b, float* c, int n)
__global__ void vectorMul(float* a, float* b, float* c, int n)
```

#### 2. 同步机制
```cuda
// 启动kernel
vectorAdd<<<blocksPerGrid, threadsPerBlock>>>(d_a, d_b, d_c, N);

// 检查启动错误
cudaGetLastError();

// 等待GPU执行完毕
cudaDeviceSynchronize();
```

#### 3. 性能测量
```cuda
cudaEvent_t start, stop;
cudaEventRecord(start);
// ... GPU操作 ...
cudaEventRecord(stop);
cudaEventSynchronize(stop);
cudaEventElapsedTime(&milliseconds, start, stop);
```

## 关键技术点

1. **同步等待**: 使用 `cudaDeviceSynchronize()` 确保每次循环都等待GPU执行完毕
2. **错误检查**: 使用宏 `CUDA_CHECK` 进行完整的错误检查
3. **内存管理**: 正确的设备内存分配和释放
4. **性能监控**: 使用CUDA事件精确测量GPU执行时间
5. **结果验证**: 将GPU结果复制回CPU进行验证

## 自定义参数

可以修改以下参数来调整程序行为：

```cpp
const int N = 1024 * 1024;  // 数组大小
const int iterations = 5;    // 循环次数
int threadsPerBlock = 256;   // 每个线程块的线程数
```

## 故障排除

### 编译错误
- 确保安装了CUDA Toolkit
- 检查nvcc编译器路径
- 验证GPU架构设置 (`-arch=sm_50`)

### 运行时错误
- 检查NVIDIA驱动程序
- 确认GPU支持所需的计算能力
- 验证内存分配是否成功

## 扩展建议

- 添加更多类型的GPU操作
- 实现异步执行对比
- 添加多GPU支持
- 集成性能分析工具
