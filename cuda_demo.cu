#include <stdio.h>
#include <stdlib.h>
#include <cuda_runtime.h>
#include <time.h>

// CUDA kernel函数：向量加法
__global__ void vectorAdd(float* a, float* b, float* c, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        c[idx] = a[idx] + b[idx];
    }
}

// CUDA kernel函数：向量乘法
__global__ void vectorMul(float* a, float* b, float* c, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        c[idx] = a[idx] * b[idx];
    }
}

// 错误检查宏
#define CUDA_CHECK(call) \
    do { \
        cudaError_t error = call; \
        if (error != cudaSuccess) { \
            fprintf(stderr, "CUDA error at %s:%d - %s\n", __FILE__, __LINE__, \
                    cudaGetErrorString(error)); \
            exit(1); \
        } \
    } while(0)

// 初始化数组
void initArray(float* arr, int n, float value) {
    for (int i = 0; i < n; i++) {
        arr[i] = value + (float)i * 0.1f;
    }
}

// 验证结果
bool verifyResult(float* a, float* b, float* c, int n, bool isAdd) {
    for (int i = 0; i < n; i++) {
        float expected = isAdd ? (a[i] + b[i]) : (a[i] * b[i]);
        if (fabs(c[i] - expected) > 1e-5) {
            printf("验证失败：索引 %d, 期望值 %.6f, 实际值 %.6f\n", i, expected, c[i]);
            return false;
        }
    }
    return true;
}

int main() {
    printf("=== CUDA Demo: CPU循环调用GPU并等待执行完毕 ===\n\n");
    
    // 参数设置
    const int N = 1024 * 1024;  // 数组大小
    const int iterations = 5;    // 循环次数
    const size_t size = N * sizeof(float);
    
    // 分配主机内存
    float *h_a, *h_b, *h_c;
    h_a = (float*)malloc(size);
    h_b = (float*)malloc(size);
    h_c = (float*)malloc(size);
    
    if (!h_a || !h_b || !h_c) {
        fprintf(stderr, "主机内存分配失败\n");
        return -1;
    }
    
    // 分配设备内存
    float *d_a, *d_b, *d_c;
    CUDA_CHECK(cudaMalloc(&d_a, size));
    CUDA_CHECK(cudaMalloc(&d_b, size));
    CUDA_CHECK(cudaMalloc(&d_c, size));
    
    // 初始化主机数据
    initArray(h_a, N, 1.0f);
    initArray(h_b, N, 2.0f);
    
    // 将数据从主机复制到设备
    CUDA_CHECK(cudaMemcpy(d_a, h_a, size, cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_b, h_b, size, cudaMemcpyHostToDevice));
    
    // 配置kernel启动参数
    int threadsPerBlock = 256;
    int blocksPerGrid = (N + threadsPerBlock - 1) / threadsPerBlock;
    
    printf("数组大小: %d 元素\n", N);
    printf("线程块大小: %d\n", threadsPerBlock);
    printf("网格大小: %d\n", blocksPerGrid);
    printf("循环次数: %d\n\n", iterations);
    
    // 创建CUDA事件用于计时
    cudaEvent_t start, stop;
    CUDA_CHECK(cudaEventCreate(&start));
    CUDA_CHECK(cudaEventCreate(&stop));
    
    // CPU循环调用GPU
    for (int i = 0; i < iterations; i++) {
        printf("--- 第 %d 次循环 ---\n", i + 1);
        
        // 记录开始时间
        CUDA_CHECK(cudaEventRecord(start));
        
        // 根据循环次数选择不同的操作
        if (i % 2 == 0) {
            printf("执行向量加法...\n");
            vectorAdd<<<blocksPerGrid, threadsPerBlock>>>(d_a, d_b, d_c, N);
        } else {
            printf("执行向量乘法...\n");
            vectorMul<<<blocksPerGrid, threadsPerBlock>>>(d_a, d_b, d_c, N);
        }
        
        // 检查kernel启动错误
        CUDA_CHECK(cudaGetLastError());
        
        // 等待GPU执行完毕（同步）
        CUDA_CHECK(cudaDeviceSynchronize());
        
        // 记录结束时间
        CUDA_CHECK(cudaEventRecord(stop));
        CUDA_CHECK(cudaEventSynchronize(stop));
        
        // 计算执行时间
        float milliseconds = 0;
        CUDA_CHECK(cudaEventElapsedTime(&milliseconds, start, stop));
        
        printf("GPU执行时间: %.3f ms\n", milliseconds);
        
        // 将结果从设备复制到主机进行验证
        CUDA_CHECK(cudaMemcpy(h_c, d_c, size, cudaMemcpyDeviceToHost));
        
        // 验证前几个结果
        bool isAdd = (i % 2 == 0);
        if (verifyResult(h_a, h_b, h_c, min(10, N), isAdd)) {
            printf("结果验证: 通过\n");
        } else {
            printf("结果验证: 失败\n");
        }
        
        printf("前5个结果: ");
        for (int j = 0; j < 5; j++) {
            printf("%.2f ", h_c[j]);
        }
        printf("\n\n");
        
        // 模拟CPU端的一些处理时间
        usleep(100000); // 休眠100ms
    }
    
    printf("=== 所有循环执行完毕 ===\n");
    
    // 清理资源
    CUDA_CHECK(cudaEventDestroy(start));
    CUDA_CHECK(cudaEventDestroy(stop));
    CUDA_CHECK(cudaFree(d_a));
    CUDA_CHECK(cudaFree(d_b));
    CUDA_CHECK(cudaFree(d_c));
    free(h_a);
    free(h_b);
    free(h_c);
    
    // 重置CUDA设备
    CUDA_CHECK(cudaDeviceReset());
    
    return 0;
}
