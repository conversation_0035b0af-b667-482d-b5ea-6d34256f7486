#include <stdio.h>
#include <stdlib.h>
#include <cuda_runtime.h>
#include <time.h>
#include <signal.h>
#include <unistd.h>

// CUDA kernel函数：向量加法
__global__ void vectorAdd(float* a, float* b, float* c, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        c[idx] = a[idx] + b[idx];
    }
}

// CUDA kernel函数：向量乘法
__global__ void vectorMul(float* a, float* b, float* c, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        c[idx] = a[idx] * b[idx];
    }
}

// 错误检查宏
#define CUDA_CHECK(call) \
    do { \
        cudaError_t error = call; \
        if (error != cudaSuccess) { \
            fprintf(stderr, "CUDA error at %s:%d - %s\n", __FILE__, __LINE__, \
                    cudaGetErrorString(error)); \
            exit(1); \
        } \
    } while(0)

// 全局变量用于控制循环
volatile sig_atomic_t keep_running = 1;

// 信号处理函数
void signal_handler(int sig) {
    printf("\n收到信号 %d，准备退出...\n", sig);
    keep_running = 0;
}

// 初始化数组
void initArray(float* arr, int n, float value) {
    for (int i = 0; i < n; i++) {
        arr[i] = value + (float)i * 0.1f;
    }
}

// 重置数组数据
void resetArray(float* arr, int n, float base_value, int iteration) {
    for (int i = 0; i < n; i++) {
        arr[i] = base_value + (float)(iteration % 10) + (float)i * 0.01f;
    }
}

// 验证结果
bool verifyResult(float* a, float* b, float* c, int n, bool isAdd) {
    for (int i = 0; i < n; i++) {
        float expected = isAdd ? (a[i] + b[i]) : (a[i] * b[i]);
        if (fabs(c[i] - expected) > 1e-5) {
            printf("验证失败：索引 %d, 期望值 %.6f, 实际值 %.6f\n", i, expected, c[i]);
            return false;
        }
    }
    return true;
}

int main() {
    printf("=== CUDA Demo: CPU死循环调用GPU并等待执行完毕 ===\n");
    printf("按 Ctrl+C 退出程序\n\n");

    // 注册信号处理函数
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 参数设置
    const int N = 1024 * 1024;  // 数组大小
    const size_t size = N * sizeof(float);
    
    // 分配主机内存
    float *h_a, *h_b, *h_c;
    h_a = (float*)malloc(size);
    h_b = (float*)malloc(size);
    h_c = (float*)malloc(size);
    
    if (!h_a || !h_b || !h_c) {
        fprintf(stderr, "主机内存分配失败\n");
        return -1;
    }
    
    // 分配设备内存
    float *d_a, *d_b, *d_c;
    CUDA_CHECK(cudaMalloc(&d_a, size));
    CUDA_CHECK(cudaMalloc(&d_b, size));
    CUDA_CHECK(cudaMalloc(&d_c, size));
    
    // 配置kernel启动参数
    int threadsPerBlock = 256;
    int blocksPerGrid = (N + threadsPerBlock - 1) / threadsPerBlock;

    printf("数组大小: %d 元素\n", N);
    printf("线程块大小: %d\n", threadsPerBlock);
    printf("网格大小: %d\n", blocksPerGrid);
    printf("开始死循环执行...\n\n");
    
    // 创建CUDA流用于异步操作
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));

    // 创建CUDA事件用于计时和同步
    cudaEvent_t start, stop;
    CUDA_CHECK(cudaEventCreate(&start));
    CUDA_CHECK(cudaEventCreate(&stop));

    // 死循环调用GPU
    int iteration = 0;
    while (keep_running) {
        iteration++;
        printf("--- 第 %d 次循环 ---\n", iteration);

        // 重置主机数据
        resetArray(h_a, N, 1.0f, iteration);
        resetArray(h_b, N, 2.0f, iteration);

        // 异步将数据从主机复制到设备
        CUDA_CHECK(cudaMemcpyAsync(d_a, h_a, size, cudaMemcpyHostToDevice, stream));
        CUDA_CHECK(cudaMemcpyAsync(d_b, h_b, size, cudaMemcpyHostToDevice, stream));

        // 记录开始时间
        CUDA_CHECK(cudaEventRecord(start, stream));

        // 根据循环次数选择不同的操作
        if (iteration % 2 == 1) {
            printf("执行向量加法...\n");
            vectorAdd<<<blocksPerGrid, threadsPerBlock, 0, stream>>>(d_a, d_b, d_c, N);
        } else {
            printf("执行向量乘法...\n");
            vectorMul<<<blocksPerGrid, threadsPerBlock, 0, stream>>>(d_a, d_b, d_c, N);
        }

        // 检查kernel启动错误
        CUDA_CHECK(cudaGetLastError());

        // 记录结束时间
        CUDA_CHECK(cudaEventRecord(stop, stream));

        // 异步将结果从设备复制到主机
        CUDA_CHECK(cudaMemcpyAsync(h_c, d_c, size, cudaMemcpyDeviceToHost, stream));

        // 等待当前流中的操作完成（只同步当前流，不影响其他GPU操作）
        CUDA_CHECK(cudaStreamSynchronize(stream));

        // 计算执行时间
        float milliseconds = 0;
        CUDA_CHECK(cudaEventElapsedTime(&milliseconds, start, stop));

        printf("GPU执行时间: %.3f ms\n", milliseconds);

        // 验证前几个结果
        bool isAdd = (iteration % 2 == 1);
        if (verifyResult(h_a, h_b, h_c, min(10, N), isAdd)) {
            printf("结果验证: 通过\n");
        } else {
            printf("结果验证: 失败\n");
        }

        printf("前5个结果: ");
        for (int j = 0; j < 5; j++) {
            printf("%.2f ", h_c[j]);
        }
        printf("\n");

        // 清零显存数据（重置）
        CUDA_CHECK(cudaMemsetAsync(d_a, 0, size, stream));
        CUDA_CHECK(cudaMemsetAsync(d_b, 0, size, stream));
        CUDA_CHECK(cudaMemsetAsync(d_c, 0, size, stream));

        // 清零主机内存数据
        memset(h_a, 0, size);
        memset(h_b, 0, size);
        memset(h_c, 0, size);

        printf("内存和显存已重置\n\n");

        // 短暂休眠，避免过度占用CPU
        usleep(50000); // 休眠50ms
    }
    
    printf("=== 循环已停止，总共执行了 %d 次 ===\n", iteration);

    // 清理资源
    CUDA_CHECK(cudaStreamDestroy(stream));
    CUDA_CHECK(cudaEventDestroy(start));
    CUDA_CHECK(cudaEventDestroy(stop));
    CUDA_CHECK(cudaFree(d_a));
    CUDA_CHECK(cudaFree(d_b));
    CUDA_CHECK(cudaFree(d_c));
    free(h_a);
    free(h_b);
    free(h_c);

    printf("资源清理完成\n");
    
    return 0;
}
